<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use App\Models\User;
use App\Models\Role;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->boolean('is_super_admin')->default(false)->after('is_active');
        });

        // Migrate existing super admin users from role to field
        $superAdminRole = Role::where('name', 'Super Admin')->first();
        if ($superAdminRole) {
            $superAdminUsers = User::whereHas('roles', function ($query) use ($superAdminRole) {
                $query->where('role_id', $superAdminRole->id);
            })->get();

            foreach ($superAdminUsers as $user) {
                $user->update(['is_super_admin' => true]);
                // Remove the super admin role from the user
                $user->removeRole($superAdminRole);
            }
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Before dropping the column, migrate super admin users back to role
        $superAdminUsers = User::where('is_super_admin', true)->get();
        
        if ($superAdminUsers->isNotEmpty()) {
            $superAdminRole = Role::where('name', 'Super Admin')->first();
            if (!$superAdminRole) {
                $superAdminRole = Role::create(['name' => 'Super Admin']);
            }

            foreach ($superAdminUsers as $user) {
                $user->assignRole($superAdminRole);
            }
        }

        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn('is_super_admin');
        });
    }
};
