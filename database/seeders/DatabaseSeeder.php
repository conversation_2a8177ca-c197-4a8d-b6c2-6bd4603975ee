<?php

namespace Database\Seeders;

use App\Models\Domain;
use App\Models\Link;
use App\Models\LinkTag;
use App\Models\Permission;
use App\Models\Role;
use App\Models\Tag;
use App\Models\User;
use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        // Create CRUD permissions for each model
        $models = ['link', 'domain', 'tag', 'user', 'role', 'permission'];
        $actions = ['view', 'create', 'update', 'delete'];

        $permissions = [];
        foreach ($models as $model) {
            foreach ($actions as $action) {
                $permissions[] = [
                    'name' => $action . ' ' . $model,
                    'guard_name' => 'web',
                    'created_at' => now(),
                    'updated_at' => now(),
                ];
            }
        }

        Permission::insert($permissions);

        Role::create([
            'name' => 'Super Admin',
        ]);

        $tagManager = Role::create([
            'name' => 'Tag Manager',
        ]);

        $tagManager->givePermissionTo([
            'create tag',
            'view tag',
            'update tag',
            'delete tag',
        ]);

        $superAdmin = User::factory()->create([
            'name' => 'Super Admin',
            'email' => '<EMAIL>',
            'password' => 'x',
            'is_super_admin' => true,
        ]);

        // Create user with specific link permissions
        $linkManager = User::factory()->create([
            'name' => 'Link Manager',
            'email' => '<EMAIL>',
            'password' => 'y',
        ]);

        // Assign specific permissions
        $linkManager->givePermissionTo([
            'create link',
            'view link',
            'update link',
            'delete link',
        ]);

        Domain::factory()->create([
            'host' => 'localhost:8080',
            'is_admin_panel_available' => true,
            'is_active' => true,
        ]);
        Domain::factory()->create([
            'host' => '*************:8080',
            'is_admin_panel_available' => false,
            'is_active' => true,
        ]);

        Link::factory()
            ->hasVisits(1000)
            ->create([
                'original_url' => 'https://google.com/',
                'slug' => 'google',
            ])
            ->domains()->attach(Domain::orderBy('id', 'desc')->first());
        Link::factory()->create([
            'original_url' => 'https://siavash-bamshadnia.com/',
            'slug' => 'siavash',
            'password' => 'x',
        ])
            ->domains()->attach(Domain::all());
        Link::factory()->create([
            'original_url' => 'https://unavailable.com/',
            'slug' => 'unavailable',
            'unavailable_at' => '2020-01-01',
        ])
            ->domains()->attach(Domain::all());
        Link::factory()->create([
            'original_url' => 'https://example.com/?a=b',
            'slug' => 'example',
            'send_ref_query_parameter' => true,
        ])
            ->domains()->attach(Domain::all());
        Link::factory(100)->create()->each(function ($link) {
            $link->domains()->attach(Domain::all());
        });

        Tag::factory(10)->create();

        LinkTag::create([
            'tag_id' => 1,
            'link_id' => 1,
        ]);
    }
}
