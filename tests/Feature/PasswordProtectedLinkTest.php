<?php


use App\Models\Domain;
use App\Models\Link;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Livewire\Livewire;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

class PasswordProtectedLinkTest extends TestCase
{
    use RefreshDatabase;

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    protected function mockForCurrentDomainScope($domain): void
    {
        // Create a partial mock of the Link model
        $linkMock = Mockery::mock('App\Models\Link')->makePartial();

        // Mock the forCurrentDomain scope to return the query builder
        $linkMock->shouldReceive('scopeForCurrentDomain')
            ->andReturnUsing(function ($query) use ($domain) {
                return $query->whereHas('domains', function (Builder $query) use ($domain) {
                    $query->where('domains.id', $domain->id);
                });
            });

        // Replace the Link model with our mock
        app()->instance('App\Models\Link', $linkMock);
    }

    #[Test]
    public function it_shows_password_form_for_protected_links()
    {
        $domain = Domain::factory()->create([
            'host' => 'example.com',
            'protocol' => 'https',
            'is_active' => true,
            'is_admin_panel_available' => true
        ]);
        $link = Link::factory()->create([
            'original_url' => 'https://target-site.com',
            'slug' => 'abc123',
            'password' => 'secret123'
        ]);
        $link->domains()->attach($domain);

        $response = $this->get('https://example.com/abc123');

        $response->assertOk()
            ->assertSeeText('Password Protected')
            ->assertSeeText('Please enter the password to access this link')
            ->assertSeeLivewire('link-page');
    }

    #[Test]
    public function it_redirects_with_correct_password()
    {
        $domain = Domain::factory()->create([
            'host' => config('app.name'),
            'protocol' => 'http',
            'is_active' => true,
            'is_admin_panel_available' => true
        ]);
        $link = Link::factory()->create([
            'original_url' => 'https://target-site.com',
            'slug' => 'abc123',
            'password' => 'secret123',
            'send_ref_query_parameter' => false,
        ]);
        $link->domains()->attach($domain);

        Livewire::test('link-page', [
            'short_path' => 'abc123',
            'link' => $link,
            'data' => [
                'password' => null,
                'errors' => []
            ]
        ])
            ->set('data.password', 'secret123')
            ->call('submit')
            ->assertRedirect('https://target-site.com');
    }

    #[Test]
    public function it_shows_error_with_wrong_password()
    {
        $domain = Domain::factory()->create([
            'host' => config('app.name'),
            'protocol' => 'http',
            'is_active' => true,
            'is_admin_panel_available' => true
        ]);
        $link = Link::factory()->create([
            'original_url' => 'https://target-site.com',
            'slug' => 'abc123',
            'password' => 'secret123'
        ]);
        $link->domains()->attach($domain);

        // Mock the forCurrentDomain scope
        $this->mockForCurrentDomainScope($domain);

        Livewire::test('link-page', [
            'short_path' => 'abc123',
            'link' => $link,
            'data' => [
                'password' => null,
                'errors' => []
            ]
        ])
            ->set('data.password', 'wrongpass')
            ->call('submit')
            ->assertNotified('Password is wrong');
    }

    #[Test]
    public function it_requires_password_field()
    {
        $domain = Domain::factory()->create([
            'host' => config('app.name'),
            'protocol' => 'http',
            'is_active' => true,
            'is_admin_panel_available' => true
        ]);
        $link = Link::factory()->create([
            'original_url' => 'https://target-site.com',
            'slug' => 'abc123',
            'password' => 'secret123'
        ]);
        $link->domains()->attach($domain);

        // Mock the forCurrentDomain scope
        $this->mockForCurrentDomainScope($domain);

        Livewire::test('link-page', [
            'short_path' => 'abc123',
            'link' => $link,
            'data' => [
                'password' => null,
                'errors' => []
            ]
        ])
            ->set('data.password', '')
            ->call('submit')
            ->assertNotified('Password is required');
    }
}
