<?php

namespace Models;

use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

class UserTest extends TestCase
{
    use RefreshDatabase;

    #[Test]
    public function it_can_create_a_user()
    {
        $user = User::factory()->create([
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'password' => 'password',
        ]);

        $this->assertDatabaseHas('users', [
            'name' => 'Test User',
            'email' => '<EMAIL>',
        ]);

        $this->assertEquals('Test User', $user->name);
        $this->assertEquals('<EMAIL>', $user->email);
    }

    #[Test]
    public function it_hashes_the_password_when_creating_a_user()
    {
        $user = User::factory()->create([
            'password' => 'password',
        ]);

        // Password should be hashed
        $this->assertNotEquals('password', $user->password);
    }

    #[Test]
    public function it_can_be_converted_to_string()
    {
        $user = User::factory()->create([
            'name' => 'Test User',
        ]);

        $this->assertEquals('Test User', (string)$user);
    }

    #[Test]
    public function it_has_roles()
    {
        $user = User::factory()->create();

        // The HasRoles trait provides methods like assignRole
        $this->assertTrue(method_exists($user, 'assignRole'));
        $this->assertTrue(method_exists($user, 'hasRole'));
    }

    #[Test]
    public function it_has_fillable_attributes()
    {
        $user = new User();

        $this->assertEquals([
            'name',
            'email',
            'password',
            'is_active',
            'is_super_admin',
        ], $user->getFillable());
    }

    #[Test]
    public function it_has_hidden_attributes()
    {
        $user = new User();

        $this->assertEquals([
            'password',
            'remember_token',
        ], $user->getHidden());
    }

    #[Test]
    public function it_has_password_cast_to_hashed()
    {
        $user = new User();
        $casts = $user->getCasts();

        $this->assertArrayHasKey('password', $casts);
        $this->assertEquals('hashed', $casts['password']);
    }

    #[Test]
    public function it_has_logs_activity_trait()
    {
        $user = new User();

        $this->assertTrue(method_exists($user, 'getActivitylogOptions'));
    }
}
