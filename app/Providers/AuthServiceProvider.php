<?php

namespace App\Providers;

use App\Models\Domain;
use App\Models\Link;
use App\Models\Permission;
use App\Models\Role;
use App\Models\Tag;
use App\Models\User;
use App\Policies\DomainPolicy;
use App\Policies\LinkPolicy;
use App\Policies\PermissionPolicy;
use App\Policies\RolePolicy;
use App\Policies\TagPolicy;
use App\Policies\UserPolicy;
use Illuminate\Foundation\Support\Providers\AuthServiceProvider as ServiceProvider;
use Illuminate\Support\Facades\Gate;

class AuthServiceProvider extends ServiceProvider
{
    /**
     * The model to policy mappings for the application.
     *
     * @var array<class-string, class-string>
     */
    protected $policies = [
        Link::class => LinkPolicy::class,
        Domain::class => DomainPolicy::class,
        Tag::class => TagPolicy::class,
        User::class => UserPolicy::class,
        Permission::class => PermissionPolicy::class,
        Role::class => RolePolicy::class,
    ];

    /**
     * Register any authentication / authorization services.
     */
    public function boot(): void
    {
        $this->registerPolicies();
        // Implicitly grant super admin users all permissions
        Gate::before(function (User $user, $ability) {
            return $user->is_super_admin ? true : null;
        });
    }
}
